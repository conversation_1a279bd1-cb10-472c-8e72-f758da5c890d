const MessageForm = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitMessage();
        _componentInputFormatting();
    };

    // Validation config using TiganiLibs
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Custom validation rules for message
        $.validator.addMethod('emailOrPhone', function (value) {
            // Email regex or phone regex
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
            return emailRegex.test(value) || phoneRegex.test(value);
        }, 'Inserire un indirizzo email valido o un numero di telefono.');

        // Define custom rules for this form
        const customRules = {
            messageType: {
                required: true
            },
            toAddress: {
                required: true,
                maxlength: 255
            },
            subject: {
                maxlength: 255
            },
            htmlBody: {
                // Will be set as required dynamically based on message type
            },
            body: {
                maxlength: 160 // For SMS
            }
        };

        // Custom options for this form
        const customOptions = {
            ignore: 'input[type=hidden], .select2-search__field'
        };

        // Initialize validation using TiganiLibs factory
        const validator = TiganiLibs.FormValidationFactory.create('.form-validate-jquery', customRules, customOptions);

    };

    // Maxlength using TiganiLibs
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Initialize maxlength using TiganiLibs
        TiganiLibs.ComponentUtils.initMaxlength('.maxlength');
    };

    const _componentDeleteButton = function () {
        // Use centralized delete button handler
        TiganiLibs.ActionHandlerFactory.initDeleteButton({
            buttonSelector: '#delete-message-btn',
            permissionCheck: 'MESSAGE_MANAGEMENT',
            permissionType: 'delete',
            entityIdAttribute: 'data-messageid',
            confirmTitle: 'Conferma eliminazione',
            confirmText: 'Sei sicuro di voler eliminare questo messaggio? Questa azione non può essere annullata.',
            confirmButtonText: 'Elimina',
            cancelButtonText: 'Annulla',
            onDelete: function(messageId, buttonElement) {
                // Call delete operation
                const formData = new FormData();
                formData.append('messageIds', messageId);
                formData.append('operation', 'delete');

                $.blockUI();
                $.ajax({
                    url: appRoutes.get('BE_MESSAGE_OPERATE'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $.unblockUI();

                        // Close offcanvas
                        const offcanvasElement = buttonElement.closest('.hs-overlay[id^="dynamic-offcanvas-"]');
                        if (offcanvasElement) {
                            const overlay = HSOverlay.getInstance(offcanvasElement, true);
                            if (overlay) {
                                overlay.element.close();
                            }
                        }

                        // Reload table
                        if (window.messagesDataTable && window.messagesDataTable.dataTable) {
                            window.messagesDataTable.dataTable.ajax.reload();
                        }

                        // Show success message
                        if (typeof showToast === 'function') {
                            showToast('Messaggio eliminato correttamente', 'success');
                        } else {
                            TiganiLibs.NotificationUtils.showSuccess('Messaggio eliminato correttamente');
                        }
                    },
                    error: function(xhr, status, error) {
                        $.unblockUI();
                        let errorMessage = 'Errore durante l\'eliminazione';
                        if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }
                        if (typeof showToast === 'function') {
                            showToast(errorMessage, 'error');
                        } else {
                            TiganiLibs.NotificationUtils.showError(errorMessage);
                        }
                        console.error('Error during message deletion:', error);
                    }
                });
            }
        });
    }

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has edit permissions for form fields
        if (!hasPermission('MESSAGE_MANAGEMENT', 'edit')) {
            // Disable all form inputs
            $('#message-edit input, #message-edit textarea, #message-edit select').prop('readonly', true);
            $('#message-edit select').prop('disabled', true);
            $('#message-edit-offcanvas input, #message-edit-offcanvas textarea, #message-edit-offcanvas select').prop('readonly', true);
            $('#message-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#message-edit, #message-edit-offcanvas').addClass('opacity-75');
        }

        // Check create permissions for new message forms
        const isNewMessage = !$('#message-edit input[name="id"]').val() && !$('#message-edit-offcanvas input[name="id"]').val();
        if (isNewMessage && !hasPermission('MESSAGE_MANAGEMENT', 'create')) {
            // Disable all form inputs
            $('#message-edit input, #message-edit textarea, #message-edit select').prop('disabled', true);
            $('#message-edit-offcanvas input, #message-edit-offcanvas textarea, #message-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#message-edit, #message-edit-offcanvas').addClass('opacity-50');
        }
    }

    const _componentSubmitMessage = function () {
        var idForm = "message-edit-offcanvas";
        // Form submission handling
        $('#' + idForm).submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();

                const formData = new FormData(this);
                const isNewMessage = !formData.get('id') || formData.get('id') === '';

                // Only allow creation, not editing
                if (!isNewMessage) {
                    showToast('I messaggi non possono essere modificati dopo l\'invio.', 'error');
                    return;
                }

                if (!hasPermission('MESSAGE_MANAGEMENT', 'create')) {
                    showToast('Non hai i permessi per inviare messaggi.', 'error');
                    return;
                }

                // Validate message type specific fields
                const messageType = formData.get('messageType');
                if (messageType === 'EMAIL') {
                    if (!formData.get('subject') || !formData.get('htmlBody')) {
                        showToast('Oggetto e contenuto HTML sono obbligatori per le email.', 'error');
                        return;
                    }
                    // Copy htmlBody to body for backend compatibility
                    formData.set('body', formData.get('htmlBody'));
                } else if (messageType === 'SMS') {
                    if (!formData.get('body')) {
                        showToast('Il testo è obbligatorio per gli SMS.', 'error');
                        return;
                    }
                    if (formData.get('body').length > 160) {
                        showToast('Il testo SMS non può superare i 160 caratteri.', 'error');
                        return;
                    }
                } else {
                    showToast('Seleziona un tipo di messaggio valido.', 'error');
                    return;
                }

                // formData.append('language', "en");
                $.blockUI();
                $.ajax({
                    url: $(this).attr("action"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        $.unblockUI();

                        try {
                            // Close offcanvas
                            const overlay = HSOverlay.getInstance(document.getElementById($("#" + idForm).closest(".hs-overlay.opened").attr("id")), true);
                            if (overlay) {
                                overlay.element.close();
                            }

                            // Reload table with error handling
                            if (window.messagesDataTable && window.messagesDataTable.dataTable) {
                                window.messagesDataTable.dataTable.ajax.reload(null, false); // Keep current page
                            } else {
                                console.warn('DataTable not found, page may need manual refresh');
                            }

                            // Show success message
                            showToast('Messaggio inviato correttamente', 'success');
                        } catch (postSuccessError) {
                            console.error('Error in post-success handling:', postSuccessError);
                            showToast('Messaggio inviato, ma si è verificato un errore nell\'aggiornamento della pagina', 'warning');
                        }
                    },
                    error: function (xhr, status, error) {
                        $.unblockUI();

                        let errorMessage = 'Errore durante il salvataggio';

                        // Handle different error types
                        if (status === 'timeout') {
                            errorMessage = 'Timeout: richiesta troppo lenta. Riprova.';
                        } else if (status === 'abort') {
                            errorMessage = 'Richiesta annullata';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Errore di connessione. Verifica la connessione internet.';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Accesso negato. Effettua nuovamente il login.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Servizio non trovato. Contatta l\'amministratore.';
                        } else if (xhr.status >= 500) {
                            errorMessage = 'Errore del server. Riprova più tardi.';
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during message save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }

    // Input formatting and validation
    const _componentInputFormatting = function() {
        // Message type field - show/hide fields based on type
        // Use Preline select change event
        const messageTypeSelect = document.querySelector('#messageType');
        if (messageTypeSelect) {
            messageTypeSelect.addEventListener('change', function() {
            const messageType = this.value;
            const subjectField = $('#subject-field');
            const textBodyField = $('#text-body-field');
            const htmlBodyField = $('#html-body-field');
            const toAddressLabel = $('#toAddressLabel');
            const toAddressHelp = $('#toAddressHelp');
            const toAddress = $('#toAddress');

            // Hide all dynamic fields first
            subjectField.hide();
            textBodyField.hide();
            htmlBodyField.hide();

            // Clear required attributes
            $('#subject').prop('required', false);
            $('#textBody').prop('required', false);
            $('#htmlBody').prop('required', false);

            if (messageType === 'EMAIL') {
                // Show email fields
                subjectField.show();
                htmlBodyField.show();

                // Set required attributes
                $('#subject').prop('required', true);
                $('#htmlBody').prop('required', true);

                // Update labels and placeholders
                toAddressLabel.html('Indirizzo Email: <span class="text-red-500">*</span>');
                toAddress.attr('placeholder', '<EMAIL>');
                toAddress.attr('type', 'email');
                toAddressHelp.text('Inserisci un indirizzo email valido');

            } else if (messageType === 'SMS') {
                // Show SMS fields
                textBodyField.show();

                // Set required attributes
                $('#textBody').prop('required', true);

                // Update labels and placeholders
                toAddressLabel.html('Numero di Telefono: <span class="text-red-500">*</span>');
                toAddress.attr('placeholder', '+393xxxxxxxxx');
                toAddress.attr('type', 'tel');
                toAddressHelp.text('Inserisci un numero di telefono con prefisso internazionale (es. +393xxxxxxxxx)');

            } else {
                // Reset to default
                toAddressLabel.html('Destinatario: <span class="text-red-500">*</span>');
                toAddress.attr('placeholder', 'Seleziona prima il tipo di messaggio');
                toAddress.attr('type', 'text');
                toAddressHelp.text('Inserisci l\'indirizzo email o il numero di telefono del destinatario');
            }
            });

            // Trigger change event on page load to set initial state
            const event = new Event('change');
            messageTypeSelect.dispatchEvent(event);
        }

        // Character counter for SMS
        $('#textBody').on('input', function() {
            const maxLength = 160;
            const currentLength = this.value.length;
            const remaining = maxLength - currentLength;

            let helpText = `${currentLength}/${maxLength} caratteri`;
            if (remaining < 20) {
                helpText += ` (${remaining} rimanenti)`;
            }

            $(this).siblings('p').text(helpText);

            if (currentLength > maxLength) {
                $(this).addClass('border-red-500');
            } else {
                $(this).removeClass('border-red-500');
            }
        });

        // Trim whitespace on blur for text fields
        $('#toAddress, #subject').on('blur', function() {
            this.value = this.value.trim();
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    MessageForm.init();
});
