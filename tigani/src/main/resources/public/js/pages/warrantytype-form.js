const WarrantyTypeForm = function () {
    // Initialization of components
    const init = function () {
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitWarrantyType();
        _componentInputFormatting();
    };

    // Validation config using TiganiLibs
    const _componentValidate = function () {
        if (!$().validate) {
            console.warn('Warning - validate.min.js is not loaded.');
            return;
        }

        // Define custom rules for this form
        const customRules = {
            code: {
                required: true,
                maxlength: 50
            },
            name: {
                required: true,
                maxlength: 100
            },
            icon: {
                maxlength: 50
            }
        };

        // Custom options for this form
        const customOptions = {
            ignore: 'input[type=hidden], .select2-search__field'
        };

        // Initialize validation using TiganiLibs factory
        const validator = TiganiLibs.FormValidationFactory.create('.form-validate-jquery', customRules, customOptions);

    };

    // Maxlength using TiganiLibs
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Initialize maxlength using TiganiLibs
        TiganiLibs.ComponentUtils.initMaxlength('.maxlength');
    };

    const _componentDeleteButton = function () {
        // Use centralized delete button handler
        TiganiLibs.ActionHandlerFactory.initDeleteButton({
            buttonSelector: '#delete-warrantytype-btn',
            permissionCheck: 'WARRANTY_TYPE_MANAGEMENT',
            permissionType: 'delete',
            entityIdAttribute: 'data-warrantytypeid',
            confirmTitle: 'Conferma eliminazione',
            confirmText: 'Sei sicuro di voler eliminare questo tipo di garanzia? Questa azione non può essere annullata.',
            confirmButtonText: 'Elimina',
            cancelButtonText: 'Annulla',
            onDelete: function(warrantyTypeId, buttonElement) {
                // Call delete operation
                const formData = new FormData();
                formData.append('warrantyTypeIds', warrantyTypeId);
                formData.append('operation', 'delete');

                $.blockUI();
                $.ajax({
                    url: appRoutes.get('BE_WARRANTY_TYPE_OPERATE'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $.unblockUI();

                        // Close offcanvas
                        const offcanvasElement = buttonElement.closest('.hs-overlay[id^="dynamic-offcanvas-"]');
                        if (offcanvasElement) {
                            const overlay = HSOverlay.getInstance(offcanvasElement, true);
                            if (overlay) {
                                overlay.element.close();
                            }
                        }

                        // Reload table
                        if (window.warrantytypesDataTable && window.warrantytypesDataTable.dataTable) {
                            window.warrantytypesDataTable.dataTable.ajax.reload();
                        }

                        // Show success message
                        if (typeof showToast === 'function') {
                            showToast('Tipo di garanzia eliminato correttamente', 'success');
                        } else {
                            TiganiLibs.NotificationUtils.showSuccess('Tipo di garanzia eliminato correttamente');
                        }
                    },
                    error: function(xhr, status, error) {
                        $.unblockUI();
                        let errorMessage = 'Errore durante l\'eliminazione';
                        if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }
                        if (typeof showToast === 'function') {
                            showToast(errorMessage, 'error');
                        } else {
                            TiganiLibs.NotificationUtils.showError(errorMessage);
                        }
                        console.error('Error during warranty type deletion:', error);
                    }
                });
            }
        });
    }

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has edit permissions for form fields
        if (!hasPermission('WARRANTY_TYPE_MANAGEMENT', 'edit')) {
            // Disable all form inputs
            $('#warrantytype-edit input, #warrantytype-edit textarea, #warrantytype-edit select').prop('readonly', true);
            $('#warrantytype-edit select').prop('disabled', true);
            $('#warrantytype-edit-offcanvas input, #warrantytype-edit-offcanvas textarea, #warrantytype-edit-offcanvas select').prop('readonly', true);
            $('#warrantytype-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#warrantytype-edit, #warrantytype-edit-offcanvas').addClass('opacity-75');
        }

        // Check create permissions for new warrantytype forms
        const isNewWarrantyType = !$('#warrantytype-edit input[name="id"]').val() && !$('#warrantytype-edit-offcanvas input[name="id"]').val();
        if (isNewWarrantyType && !hasPermission('WARRANTY_TYPE_MANAGEMENT', 'create')) {
            // Disable all form inputs
            $('#warrantytype-edit input, #warrantytype-edit textarea, #warrantytype-edit select').prop('disabled', true);
            $('#warrantytype-edit-offcanvas input, #warrantytype-edit-offcanvas textarea, #warrantytype-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#warrantytype-edit, #warrantytype-edit-offcanvas').addClass('opacity-50');
        }
    }

    const _componentSubmitWarrantyType = function () {
        var idForm = "warrantytype-edit-offcanvas";
        // Form submission handling
        $('#' + idForm).submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();
                e.stopPropagation();

                const formData = new FormData(this);
                const isNewWarrantyType = !formData.get('id') || formData.get('id') === '';

                // Check permissions based on operation type
                if (isNewWarrantyType) {
                    if (!hasPermission('WARRANTY_TYPE_MANAGEMENT', 'create')) {
                        showToast('Non hai i permessi per creare tipi di garanzia.', 'error');
                        return;
                    }
                } else {
                    if (!hasPermission('WARRANTY_TYPE_MANAGEMENT', 'edit')) {
                        showToast('Non hai i permessi per modificare tipi di garanzia.', 'error');
                        return;
                    }
                }

                // formData.append('language', "en");
                $.blockUI();
                $.ajax({
                    url: $(this).attr("action"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        $.unblockUI();

                        try {
                            // Close offcanvas
                            const overlay = HSOverlay.getInstance(document.getElementById($("#" + idForm).closest(".hs-overlay.opened").attr("id")), true);
                            if (overlay) {
                                overlay.element.close();
                            }

                            // Reload table with error handling
                            if (window.warrantytypesDataTable && window.warrantytypesDataTable.dataTable) {
                                window.warrantytypesDataTable.dataTable.ajax.reload(null, false); // Keep current page
                            } else {
                                console.warn('DataTable not found, page may need manual refresh');
                            }

                            // Show success message
                            showToast('Tipo di garanzia salvato correttamente', 'success');
                        } catch (postSuccessError) {
                            console.error('Error in post-success handling:', postSuccessError);
                            showToast('Tipo di garanzia salvato, ma si è verificato un errore nell\'aggiornamento della pagina', 'warning');
                        }
                    },
                    error: function (xhr, status, error) {
                        $.unblockUI();

                        let errorMessage = 'Errore durante il salvataggio';

                        // Handle different error types
                        if (status === 'timeout') {
                            errorMessage = 'Timeout: richiesta troppo lenta. Riprova.';
                        } else if (status === 'abort') {
                            errorMessage = 'Richiesta annullata';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Errore di connessione. Verifica la connessione internet.';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Accesso negato. Effettua nuovamente il login.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Servizio non trovato. Contatta l\'amministratore.';
                        } else if (xhr.status >= 500) {
                            errorMessage = 'Errore del server. Riprova più tardi.';
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during warranty type save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }

    // Input formatting and validation
    const _componentInputFormatting = function() {
        // Code field - uppercase transformation
        $('input[name="code"]').on('input', function() {
            this.value = this.value.toUpperCase();
        });

        // Name field - trim whitespace
        $('input[name="name"]').on('blur', function() {
            this.value = this.value.trim();
        });

        // Icon field - trim whitespace
        $('input[name="icon"]').on('blur', function() {
            this.value = this.value.trim();
        });
    };

    // Return objects assigned to module
    return {
        init: init
    };
}();

// Initialize module
// ------------------------------

document.addEventListener('DOMContentLoaded', function () {
    WarrantyTypeForm.init();
});
