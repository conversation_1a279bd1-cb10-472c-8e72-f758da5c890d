var oldImageBase64;

const UserForm = function () {
    var pond;

    // Initialization of components
    const init = function () {
        _componentFilePond();
        _componentValidate();
        _componentMaxlength();
        _componentDeleteButton();
        _componentPermissionChecks();
        _componentSubmitUser();
    };

    // FilePond using centralized factory
    const _componentFilePond = function () {
        pond = TiganiLibs.UIComponentFactory.initFilePond('input[type="file"]', {});

        // Load initial image if present
        var imageId = pageVariables.get("imageId");
        if (typeof imageId !== "undefined" && imageId && pond) {
            var image = appRoutes.get("BE_IMAGE") + "?oid=" + pageVariables.get("imageId").replace("[", "").replace("]", "");
            pond.addFile(image).then(file => {
                oldImageBase64 = file.getFileEncodeBase64String();
            }).catch(err => {
                console.error('Error loading image', err);
            });
        }
    };

    // Validation using centralized factory
    const _componentValidate = function () {
        // Custom validation options for user form
        const customOptions = {};

        // Use centralized form validation factory
        const validator = TiganiLibs.FormValidationFactory.create('.form-validate-jquery', null, customOptions);

        // custom url validation
        $.validator.addMethod('identifier', function (value) {
            return /^[a-z0-9-_]+$/.test(value);
        }, 'URL non valido. L\'identificatore deve contenere solo lettere minuscole, numeri, trattini e sottolineature.');

    };

    // Maxlength
    const _componentMaxlength = function () {
        if (!$().maxlength) {
            console.warn('Warning - maxlength.min.js is not loaded.');
            return;
        }

        // Basic example
        $('.maxlength').maxlength({
            placement: document.dir === "rtl" ? 'top-left-inside' : 'top-right-inside',
            warningClass: 'bootstrap-maxlength text-muted form-text m-0',
            limitReachedClass: 'bootstrap-maxlength text-danger form-text m-0'
        });

    };

    const _componentDeleteButton = function () {
        // Use centralized delete button handler
        TiganiLibs.ActionHandlerFactory.initDeleteButton({
            buttonSelector: '#delete-user-btn',
            permissionCheck: 'USER_MANAGEMENT',
            permissionType: 'delete',
            entityIdAttribute: 'data-userid',
            confirmTitle: 'Conferma eliminazione',
            confirmText: 'Sei sicuro di voler eliminare questo utente? Questa azione non può essere annullata.',
            confirmButtonText: 'Elimina',
            cancelButtonText: 'Annulla',
            onDelete: function(userId, buttonElement) {
                // Call delete operation
                const formData = new FormData();
                formData.append('userIds', userId);
                formData.append('operation', 'delete');

                $.blockUI();
                $.ajax({
                    url: appRoutes.get('BE_USER_OPERATE'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $.unblockUI();

                        // Close offcanvas
                        const offcanvasElement = buttonElement.closest('.hs-overlay[id^="dynamic-offcanvas-"]');
                        if (offcanvasElement) {
                            const overlay = HSOverlay.getInstance(offcanvasElement, true);
                            if (overlay) {
                                overlay.element.close();
                            }
                        }

                        // Reload table
                        if (window.usersDataTable && window.usersDataTable.dataTable) {
                            window.usersDataTable.dataTable.ajax.reload();
                        }

                        // Show success message
                        if (typeof showToast === 'function') {
                            showToast('Utente eliminato correttamente', 'success');
                        } else {
                            TiganiLibs.NotificationUtils.showSuccess('Utente eliminato correttamente');
                        }
                    },
                    error: function(xhr, status, error) {
                        $.unblockUI();
                        let errorMessage = 'Errore durante l\'eliminazione';
                        if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }
                        if (typeof showToast === 'function') {
                            showToast(errorMessage, 'error');
                        } else {
                            TiganiLibs.NotificationUtils.showError(errorMessage);
                        }
                        console.error('Error during user deletion:', error);
                    }
                });
            }
        });
    }

    // Permission checks
    const _componentPermissionChecks = function () {
        // Check if user has edit permissions for form fields
        if (!hasPermission('USER_MANAGEMENT', 'edit')) {
            // Disable all form inputs
            $('#user-edit input, #user-edit textarea, #user-edit select').prop('readonly', true);
            $('#user-edit select').prop('disabled', true);
            $('#user-edit-offcanvas input, #user-edit-offcanvas textarea, #user-edit-offcanvas select').prop('readonly', true);
            $('#user-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#user-edit, #user-edit-offcanvas').addClass('opacity-75');
        }

        // Check create permissions for new user forms
        const isNewUser = !$('#user-edit input[name="id"]').val() && !$('#user-edit-offcanvas input[name="id"]').val();
        if (isNewUser && !hasPermission('USER_MANAGEMENT', 'create')) {
            // Disable all form inputs
            $('#user-edit input, #user-edit textarea, #user-edit select').prop('disabled', true);
            $('#user-edit-offcanvas input, #user-edit-offcanvas textarea, #user-edit-offcanvas select').prop('disabled', true);

            // Add visual indication
            $('#user-edit, #user-edit-offcanvas').addClass('opacity-50');
        }
    }

    const _componentSubmitUser = function () {
        var idForm = "user-edit-offcanvas";
        // Form submission handling
        $('#' + idForm).submit(function (e) {
            if ($(this).valid()) {
                e.preventDefault();

                const formData = new FormData(this);
                const isNewUser = !formData.get('id') || formData.get('id') === '';

                // Check permissions based on operation type
                if (isNewUser) {
                    if (!hasPermission('USER_MANAGEMENT', 'create')) {
                        showToast('Non hai i permessi per creare utenti.', 'error');
                        return;
                    }
                } else {
                    if (!hasPermission('USER_MANAGEMENT', 'edit')) {
                        showToast('Non hai i permessi per modificare utenti.', 'error');
                        return;
                    }
                }

                // Handle FilePond file if present
                try {
                    if (pond.getFiles().length > 0) {
                        // Ottieni la stringa base64 del file croppato
                        const fileToInsert = pond.getFiles()[0];
                        const base64String = fileToInsert.getFileEncodeBase64String();

                        if (oldImageBase64 === base64String) {
                            // add field to specify that image is the same and should not be updated
                            formData.append('sameImage', true);
                        } else {
                            const mimeType = fileToInsert.fileType;
                            const blob = base64ToBlob(base64String, mimeType);
                            const fileName = fileToInsert.filename;
                            const file = new File([blob], fileName, {type: mimeType});

                            // Aggiungi il file croppato al FormData
                            formData.append('file', file);
                        }
                    }
                } catch (fileError) {
                    console.warn('Error processing file upload:', fileError);
                    // Continue without file if there's an error
                }

                // formData.append('language', "en");
                $.blockUI();
                $.ajax({
                    url: $(this).attr("action"),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        $.unblockUI();

                        try {
                            // Close offcanvas
                            const overlay = HSOverlay.getInstance(document.getElementById($("#" + idForm).closest(".hs-overlay.opened").attr("id")), true);
                            if (overlay) {
                                overlay.element.close();
                            }

                            // Reload table with error handling
                            if (window.usersDataTable && window.usersDataTable.dataTable) {
                                window.usersDataTable.dataTable.ajax.reload(null, false); // Keep current page
                            } else {
                                console.warn('DataTable not found, page may need manual refresh');
                            }

                            // Show success message
                            showToast('Utente salvato correttamente', 'success');
                        } catch (postSuccessError) {
                            console.error('Error in post-success handling:', postSuccessError);
                            showToast('Utente salvato, ma si è verificato un errore nell\'aggiornamento della pagina', 'warning');
                        }
                    },
                    error: function (xhr, status, error) {
                        $.unblockUI();

                        let errorMessage = 'Errore durante il salvataggio';

                        // Handle different error types
                        if (status === 'timeout') {
                            errorMessage = 'Timeout: richiesta troppo lenta. Riprova.';
                        } else if (status === 'abort') {
                            errorMessage = 'Richiesta annullata';
                        } else if (xhr.status === 0) {
                            errorMessage = 'Errore di connessione. Verifica la connessione internet.';
                        } else if (xhr.status === 403) {
                            errorMessage = 'Accesso negato. Effettua nuovamente il login.';
                        } else if (xhr.status === 404) {
                            errorMessage = 'Servizio non trovato. Contatta l\'amministratore.';
                        } else if (xhr.status >= 500) {
                            errorMessage = 'Errore del server. Riprova più tardi.';
                        } else if (xhr.responseText) {
                            errorMessage = xhr.responseText;
                        }

                        showToast(errorMessage, 'error');
                        console.error('Error during user save/update:', {
                            status: status,
                            error: error,
                            xhr: xhr
                        });
                    }
                });
            }
        });
    }
    
    // Return objects assigned to module
    return {
        init: init
    };
}();

// Funzione ausiliaria per convertire base64 in Blob
function base64ToBlob(base64, mimeType) {
    const byteCharacters = atob(base64);
    const byteArrays = [];

    for (let i = 0; i < byteCharacters.length; i++) {
        byteArrays.push(byteCharacters.charCodeAt(i));
    }

    const byteArray = new Uint8Array(byteArrays);
    return new Blob([byteArray], {type: mimeType});
}