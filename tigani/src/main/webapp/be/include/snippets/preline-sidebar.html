<!-- Sidebar -->
<aside id="hs-pro-sidebar" class="hs-overlay [--auto-close:lg] 
       hs-overlay-open:translate-x-0
       -translate-x-full transition-all duration-300 transform
       w-65 h-full
       hidden
       fixed inset-y-0 z-60
       bg-white lg:bg-transparent
       lg:block lg:translate-x-0 lg:inset-y-auto" tabindex="-1" aria-label="Detached Sidebar">
    <div class="flex flex-col h-full max-h-full pt-5">
        <!-- Content -->
        <div class="h-full overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
            <!-- Nav -->
            <nav class="hs-accordion-group p-5 pt-0 w-full flex flex-col flex-wrap" data-hs-accordion-always-open>
                <ul class="flex flex-col gap-y-1">

                    <!-- Link -->
                    <li>
                        <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg {{ menu == 'DASHBOARD' ? 'bg-gray-100 dark:bg-neutral-700' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="{{ routes('BE_DASHBOARD') }}">
                            <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" ><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9 22 9 12 15 12 15 22"/></svg>
                            Dashboard
                        </a>
                    </li>
                    <!-- End Link -->

                    {% if user.hasPermission('CATALOG_MANAGEMENT', 'view') %}
                        <!-- Link -->
                        <li>
                            <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg {{ menu == 'CATALOG_COLLECTION' ? 'bg-gray-100 dark:bg-neutral-700' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="{{ routes('BE_CATALOG_COLLECTION') }}">                                                                
                                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-library-big-icon lucide-library-big"><rect width="8" height="18" x="3" y="3" rx="1"/><path d="M7 3v18"/><path d="M20.4 18.9c.2.5-.1 1.1-.6 1.3l-1.9.7c-.5.2-1.1-.1-1.3-.6L11.1 5.1c-.2-.5.1-1.1.6-1.3l1.9-.7c.5-.2 1.1.1 1.3.6Z"/></svg>
                                Prodotti
                            </a>
                        </li>
                        <!-- End Link -->
                    {% endif %}  
                    
                    {% if user.hasPermission('ESTIMATE_MANAGEMENT', 'view') %}
                        <!-- Link -->
                        <li>
                            <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg {{ menu == 'ESTIMATE_COLLECTION' ? 'bg-gray-100 dark:bg-neutral-700' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="{{ routes('BE_ESTIMATE_COLLECTION') }}">                                                                
                                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M12 13v7a2 2 0 0 0 4 0"/>
                                    <path d="M12 2v2"/>
                                    <path d="M20.992 13a1 1 0 0 0 .97-1.274 10.284 10.284 0 0 0-19.923 0A1 1 0 0 0 3 13z"/>
                                </svg>
                                Trattative
                            </a>
                        </li>
                        <!-- End Link -->
                    {% endif %} 
                    
                    {% if user.hasPermission('CONTACT_MANAGEMENT', 'view') %}
                        <!-- Link -->
                        <li>
                            <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg {{ menu == 'CONTACT_COLLECTION' ? 'bg-gray-100 dark:bg-neutral-700' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="{{ routes('BE_CONTACT_COLLECTION') }}">
                                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-users-icon lucide-users"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><path d="M16 3.128a4 4 0 0 1 0 7.744"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><circle cx="9" cy="7" r="4"/></svg>
                                Anagrafiche
                            </a>
                        </li>
                        <!-- End Link -->
                    {% endif %}                                        
                    
                    {% if user.hasPermission('DEALER_MANAGEMENT', 'view') %}
                        <!-- Link -->
                        <li>
                            <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg {{ menu == 'DEALER_COLLECTION' ? 'bg-gray-100 dark:bg-neutral-700' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="{{ routes('BE_DEALER_COLLECTION') }}">                                
                                <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-store-icon lucide-store"><path d="M15 21v-5a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v5"/><path d="M17.774 10.31a1.12 1.12 0 0 0-1.549 0 2.5 2.5 0 0 1-3.451 0 1.12 1.12 0 0 0-1.548 0 2.5 2.5 0 0 1-3.452 0 1.12 1.12 0 0 0-1.549 0 2.5 2.5 0 0 1-3.77-3.248l2.889-4.184A2 2 0 0 1 7 2h10a2 2 0 0 1 1.653.873l2.895 4.192a2.5 2.5 0 0 1-3.774 3.244"/><path d="M4 10.95V19a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8.05"/></svg>
                                Rivenditori
                            </a>
                        </li>
                        <!-- End Link -->
                    {% endif %}
                    
                    
                    <!-- Link -->
                    <li class="hs-accordion {{ menu == 'MAINTENANCE' ? 'active' : '' }}" id="dashboard-accordion">
                        <button type="button" class="hs-accordion-toggle hs-accordion-active:bg-gray-100 w-full text-start flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg {{ menu == 'MAINTENANCE' ? 'bg-gray-100' : 'hover:bg-gray-100' }} disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hs-accordion-active:bg-neutral-700 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" aria-expanded="false" aria-controls="dashboard-accordion-sub">
                            <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915"/><circle cx="12" cy="12" r="3"/></svg>
                            Manutenzione
                            <svg class="hs-accordion-active:-rotate-180 shrink-0 mt-1 size-4 ms-auto transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                        </button>

                        <div id="dashboard-accordion-sub" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300 hidden" role="region" aria-labelledby="dashboard-accordion"
                             style="display:{{ menu == 'MAINTENANCE' ? 'block' : '' }}">
                            <ul class="hs-accordion-group ps-7 mt-1 flex flex-col gap-y-1 relative before:absolute before:top-0 before:start-[19px] before:w-0.5 before:h-full before:bg-gray-100 dark:before:bg-neutral-700" data-hs-accordion-always-open>
                                
                                {% if user.hasPermission('PRODUCT_MANAGEMENT', 'view') %}
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg {{ submenu == 'PRODUCT_COLLECTION' ? 'bg-gray-100' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 dark:bg-neutral-700" href="{{ routes('BE_PRODUCT_COLLECTION') }}">
                                        Prodotti
                                    </a>
                                </li>
                                {% endif %}
                                
                                {% if user.hasPermission('USER_MANAGEMENT', 'view') %}
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg {{ submenu == 'USER_COLLECTION' ? 'bg-gray-100' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 dark:bg-neutral-700" href="{{ routes('BE_USER_COLLECTION') }}">
                                        Utenti
                                    </a>
                                </li>
                                {% endif %}

                                {% if user.hasPermission('COUNTRY_MANAGEMENT', 'view') %}
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg {{ submenu == 'COUNTRY_COLLECTION' ? 'bg-gray-100' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 dark:bg-neutral-700" href="{{ routes('BE_COUNTRY_COLLECTION') }}">
                                        Paesi
                                    </a>
                                </li>
                                {% endif %}

                                {% if user.hasPermission('PROVINCE_MANAGEMENT', 'view') %}
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg {{ submenu == 'PROVINCE_COLLECTION' ? 'bg-gray-100' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 dark:bg-neutral-700" href="{{ routes('BE_PROVINCE_COLLECTION') }}">
                                        Province
                                    </a>
                                </li>
                                {% endif %}

                                {% if user.hasPermission('CITY_MANAGEMENT', 'view') %}
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg {{ submenu == 'CITY_COLLECTION' ? 'bg-gray-100' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 dark:bg-neutral-700" href="{{ routes('BE_CITY_COLLECTION') }}">
                                        Città
                                    </a>
                                </li>
                                {% endif %}

                                {% if user.hasPermission('BRAND_MANAGEMENT', 'view') %}
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg {{ submenu == 'BRAND_COLLECTION' ? 'bg-gray-100' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 dark:bg-neutral-700" href="{{ routes('BE_BRAND_COLLECTION') }}">
                                        Marchi
                                    </a>
                                </li>
                                {% endif %}

                                {% if user.hasPermission('MESSAGE_MANAGEMENT', 'view') %}
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg {{ submenu == 'MESSAGE_COLLECTION' ? 'bg-gray-100' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 dark:bg-neutral-700" href="{{ routes('BE_MESSAGE_COLLECTION') }}">
                                        Messaggi
                                    </a>
                                </li>
                                {% endif %}

                                {% if user.hasPermission('MODEL_MANAGEMENT', 'view') %}
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg {{ submenu == 'MODEL_COLLECTION' ? 'bg-gray-100' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 dark:bg-neutral-700" href="{{ routes('BE_MODEL_COLLECTION') }}">
                                        Modelli
                                    </a>
                                </li>
                                {% endif %}

                                {% if user.hasPermission('MODELSETUP_MANAGEMENT', 'view') %}
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg {{ submenu == 'MODELSETUP_COLLECTION' ? 'bg-gray-100' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 dark:bg-neutral-700" href="{{ routes('BE_MODELSETUP_COLLECTION') }}">
                                        Allestimenti Modelli
                                    </a>
                                </li>
                                {% endif %}

                                {% if user.hasPermission('WARRANTY_TYPE_MANAGEMENT', 'view') %}
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg {{ submenu == 'WARRANTYTYPE_COLLECTION' ? 'bg-gray-100' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 dark:bg-neutral-700" href="{{ routes('BE_WARRANTYTYPE_COLLECTION') }}">
                                        Tipi di Garanzia
                                    </a>
                                </li>
                                {% endif %}

                                {% if user.hasPermission('WARRANTY_MANAGEMENT', 'view') %}
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg {{ submenu == 'WARRANTY_COLLECTION' ? 'bg-gray-100' : 'hover:bg-gray-100' }} focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700 dark:bg-neutral-700" href="{{ routes('BE_WARRANTY_COLLECTION') }}">
                                        Garanzie
                                    </a>
                                </li>
                                {% endif %}

                                {% if user.hasPermission('PERMISSION_MANAGEMENT', 'view') %}
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="{{ routes('BE_USER_PERMISSIONS_MANAGER') }}">
                                        Permessi Utenti
                                    </a>
                                </li>
                                {% endif %}
                                {% if user.hasPermission('SYSTEM_ADMINISTRATION', 'view') %}
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="{{ routes('BE_TABLE_COLLECTION') }}">
                                        Tabelle
                                    </a>
                                </li>
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700"
                                       href="{{ routes('BE_IMPORT') }}">
                                        Import
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                    </li>
                    <!-- End Link -->
                    {#
                    <!-- Link -->
                    <li class="hs-accordion" id="users-accordion">
                        <button type="button" class="hs-accordion-toggle hs-accordion-active:bg-gray-100 w-full text-start flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hs-accordion-active:bg-neutral-700 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" aria-expanded="false" aria-controls="users-accordion-sub">
                            <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" ><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>
                            Users

                            <svg class="hs-accordion-active:-rotate-180 shrink-0 mt-1 size-4 ms-auto transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                        </button>

                        <div id="users-accordion-sub" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300 hidden" role="region" aria-labelledby="users-accordion">
                            <ul class="hs-accordion-group ps-7 mt-1 flex flex-col gap-y-1 relative before:absolute before:top-0 before:start-4.5 before:w-0.5 before:h-full before:bg-gray-100 dark:before:bg-neutral-700" data-hs-accordion-always-open>
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                                        Overview
                                    </a>
                                </li>
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                                        Add User
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <!-- End Link -->

                    <!-- Link -->
                    <li class="hs-accordion" id="user-profile-accordion">
                        <button type="button" class="hs-accordion-toggle hs-accordion-active:bg-gray-100 w-full text-start flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hs-accordion-active:bg-neutral-700 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" aria-expanded="false" aria-controls="user-profile-accordion-sub">
                            <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><circle cx="12" cy="10" r="3"/><path d="M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662"/></svg>
                            Profile

                            <svg class="hs-accordion-active:-rotate-180 shrink-0 mt-1 size-4 ms-auto transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                        </button>

                        <div id="user-profile-accordion-sub" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300 hidden" role="region" aria-labelledby="user-profile-accordion">
                            <ul class="hs-accordion-group ps-7 mt-1 flex flex-col gap-y-1 relative before:absolute before:top-0 before:start-4.5 before:w-0.5 before:h-full before:bg-gray-100 dark:before:bg-neutral-700" data-hs-accordion-always-open>
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                                        My Profile
                                    </a>
                                </li>
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                                        Profile
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <!-- End Link -->

                    <!-- Link -->
                    <li class="hs-accordion" id="account-accordion">
                        <button type="button" class="hs-accordion-toggle hs-accordion-active:bg-gray-100 w-full text-start flex gap-x-3 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:hs-accordion-active:bg-neutral-700 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" aria-expanded="false" aria-controls="user-profile-accordion-sub">
                            <svg class="shrink-0 mt-0.5 size-4"  xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="18" cy="15" r="3"/><circle cx="9" cy="7" r="4"/><path d="M10 15H6a4 4 0 0 0-4 4v2"/><path d="m21.7 16.4-.9-.3"/><path d="m15.2 13.9-.9-.3"/><path d="m16.6 18.7.3-.9"/><path d="m19.1 12.2.3-.9"/><path d="m19.6 18.7-.4-1"/><path d="m16.8 12.3-.4-1"/><path d="m14.3 16.6 1-.4"/><path d="m20.7 13.8 1-.4"/></svg>
                            Account

                            <svg class="hs-accordion-active:-rotate-180 shrink-0 mt-1 size-4 ms-auto transition" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                        </button>

                        <div id="account-accordion-sub" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300 hidden" role="region" aria-labelledby="account-accordion">
                            <ul class="hs-accordion-group ps-7 mt-1 flex flex-col gap-y-1 relative before:absolute before:top-0 before:start-4.5 before:w-0.5 before:h-full before:bg-gray-100 dark:before:bg-neutral-700" data-hs-accordion-always-open>
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                                        Profile
                                    </a>
                                </li>
                                <li>
                                    <a class="flex gap-x-4 py-2 px-3 text-sm text-gray-800 rounded-lg hover:bg-gray-100 focus:outline-hidden focus:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                                        Notifications
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <!-- End Link -->

                    <!-- Link -->
                    <li>
                        <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 focus:outline-hidden focus:bg-gray-100 rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                            <svg class="shrink-0 mt-0.5 size-4 text-blue-600 dark:text-blue-500" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="m16 12-4-4-4 4"/><path d="M12 16V8"/></svg>
                            <span class="bg-clip-text bg-linear-to-tr from-blue-600 to-purple-600 to-80% text-transparent dark:from-blue-500 dark:to-purple-500">
                                Upgrade to PRO
                            </span>
                        </a>
                    </li>
                    <!-- End Link -->

                    <!-- Link -->
                    <li>
                        <a class="flex gap-x-3 py-2 px-3 text-sm text-gray-800 focus:outline-hidden focus:bg-gray-100 rounded-lg hover:bg-gray-100 dark:hover:bg-neutral-700 dark:text-neutral-300 dark:focus:bg-neutral-700" href="#">
                            <svg class="shrink-0 mt-0.5 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9"/><path d="M10.3 21a1.94 1.94 0 0 0 3.4 0"/></svg>
                            What’s New
                            <div class="ms-auto">
                                <span class="inline-flex items-center gap-1.5 py-px px-1.5 rounded-lg text-[10px] leading-4 font-medium bg-white border border-gray-200 text-gray-800 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                    v1.0
                                </span>
                            </div>
                        </a>
                    </li>
                    <!-- End Link -->
                    
                    #}
                </ul>
            </nav>
            <!-- End Nav -->
        </div>
        <!-- End Content -->



        <div class="lg:hidden absolute top-3 -end-3 z-10">
            <!-- Sidebar Close -->
            <button type="button" class="w-6 h-7 inline-flex justify-center items-center gap-x-2 text-sm font-medium rounded-md border border-gray-200 bg-white text-gray-500 hover:bg-gray-50 focus:outline-hidden focus:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700" data-hs-overlay="#hs-pro-sidebar">
                <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="7 8 3 12 7 16"/><line x1="21" x2="11" y1="12" y2="12"/><line x1="21" x2="11" y1="6" y2="6"/><line x1="21" x2="11" y1="18" y2="18"/></svg>
            </button>
            <!-- End Sidebar Close -->
        </div>
    </div>
</aside>
<!-- End Sidebar -->