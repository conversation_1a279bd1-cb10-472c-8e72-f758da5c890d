<!-- Message Form Content for Offcanvas -->
<script class="reload-script-on-load">
    addRoute('BE_MESSAGE', '{{ routes("BE_MESSAGE") }}');
    addRoute('BE_MESSAGE_SAVE', '{{ routes("BE_MESSAGE_SAVE") }}');
    addRoute('BE_MESSAGE_OPERATE', '{{ routes("BE_MESSAGE_OPERATE") }}');
</script>

<!-- Form Content with Sticky Footer -->
<div class="flex flex-col h-full">
    <!-- Scrollable Content Area -->
    <div class="flex-1 overflow-y-auto">
        <div class="p-4">
            {% set postUrl = routes('BE_MESSAGE_SAVE') %}
            {% if curMessage.id is not empty %}
            {% set postUrl = routes('BE_MESSAGE_SAVE') + '?messageId=' + curMessage.id %}
            {% endif %}

            <form id="message-edit-offcanvas" method="POST" action="{{ postUrl }}" enctype="multipart/form-data" class="form-validate-jquery">
                <!-- Hidden ID field -->
                <input type="hidden" name="id" value="{{ curMessage.id }}">

                <!-- Message Type -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Tipo Messaggio: <span class="text-red-500">*</span>
                    </label>
                    <select name="messageType" id="messageType" data-hs-select='{
                        "placeholder": "Seleziona tipo messaggio...",
                        "toggleTag": "<button type=\"button\" aria-expanded=\"false\"></button>",
                        "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-3 pe-9 flex gap-x-2 text-nowrap w-full cursor-pointer bg-white border border-gray-200 rounded-lg text-start text-sm focus:outline-hidden focus:ring-2 focus:ring-blue-500 dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:focus:outline-hidden dark:focus:ring-1 dark:focus:ring-neutral-600",
                        "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                        "optionClasses": "py-2 px-4 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                        "optionTemplate": "<div class=\"flex justify-between items-center w-full\"><span data-title></span><span class=\"hidden hs-selected:block\"><svg class=\"shrink-0 size-3.5 text-blue-600 dark:text-blue-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"20 6 9 17 4 12\"/></svg></span></div>",
                        "extraMarkup": "<div class=\"absolute top-1/2 end-3 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                    }' class="hidden" required {% if curMessage.id is not empty %}disabled{% endif %}>
                        <option value="">Seleziona tipo messaggio</option>
                        <option value="EMAIL" {% if curMessage.messageType == 'EMAIL' %}selected{% endif %}>Email</option>
                        <option value="SMS" {% if curMessage.messageType == 'SMS' %}selected{% endif %}>SMS</option>
                    </select>
                    {% if curMessage.id is not empty %}
                    <p class="text-xs text-gray-500 dark:text-neutral-400 mt-1">Il tipo di messaggio non può essere modificato dopo la creazione</p>
                    {% endif %}
                </div>

                <!-- To Address -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2" id="toAddressLabel">
                        Destinatario: <span class="text-red-500">*</span>
                    </label>
                    <input name="toAddress" id="toAddress" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Seleziona prima il tipo di messaggio" value="{{ curMessage.toAddress }}" required maxlength="255" {% if curMessage.id is not empty %}readonly{% endif %}>
                    <p class="text-xs text-gray-500 dark:text-neutral-400 mt-1" id="toAddressHelp">Inserisci l'indirizzo email o il numero di telefono del destinatario</p>
                </div>

                <!-- Subject (for emails only) -->
                <div class="mb-4" id="subject-field" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Oggetto: <span class="text-red-500">*</span>
                    </label>
                    <input name="subject" id="subject" type="text" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600 maxlength" placeholder="Oggetto dell'email" value="{{ curMessage.subject }}" maxlength="255" {% if curMessage.id is not empty %}readonly{% endif %}>
                </div>

                <!-- Text Body (for SMS) -->
                <div class="mb-4" id="text-body-field" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Testo SMS: <span class="text-red-500">*</span>
                    </label>
                    <textarea name="body" id="textBody" rows="4" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Testo del messaggio SMS (max 160 caratteri)" maxlength="160" {% if curMessage.id is not empty %}readonly{% endif %}>{{ curMessage.body }}</textarea>
                    <p class="text-xs text-gray-500 dark:text-neutral-400 mt-1">Massimo 160 caratteri per SMS</p>
                </div>

                <!-- HTML Body (for emails) -->
                <div class="mb-4" id="html-body-field" style="display: none;">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Contenuto Email: <span class="text-red-500">*</span>
                    </label>
                    {% if curMessage.id is not empty %}
                    <div class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400">
                        {% autoescape false %}
                            {{ curMessage.htmlBody }}
                        {% endautoescape %}
                    </div>
                    {% else %}
                        <textarea name="htmlBody" id="htmlBody" rows="8" class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none dark:bg-neutral-900 dark:border-neutral-700 dark:text-neutral-400 dark:placeholder-neutral-500 dark:focus:ring-neutral-600" placeholder="Contenuto HTML dell'email" {% if curMessage.id is not empty %}readonly{% endif %}>{{ curMessage.htmlBody | removeHtmlTags }}</textarea>
                        <p class="text-xs text-gray-500 dark:text-neutral-400 mt-1">Puoi utilizzare HTML per formattare l'email</p>
                    {% endif %}
                </div>

                <!-- Status (read-only for existing messages) -->
                {% if curMessage.id is not empty %}
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 dark:text-neutral-300 mb-2">
                        Stato:
                    </label>
                    <div class="py-2 px-3 block w-full border-gray-200 rounded-lg text-sm bg-gray-50 dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-400">
                        {% if curMessage.status == 'PENDING' %}In attesa
                        {% elseif curMessage.status == 'SENT' %}Inviato
                        {% elseif curMessage.status == 'DELIVERED' %}Consegnato
                        {% elseif curMessage.status == 'FAILED' %}Fallito
                        {% elseif curMessage.status == 'BOUNCED' %}Rimbalzato
                        {% elseif curMessage.status == 'OPENED' %}Aperto
                        {% elseif curMessage.status == 'CLICKED' %}Cliccato
                        {% else %}{{ curMessage.status }}
                        {% endif %}
                    </div>

                    <!-- Delivery Information -->
                    {% if curMessage.sentAt is not empty %}
                    <div class="mt-2 text-xs text-gray-500 dark:text-neutral-400">
                        <p><strong>Inviato:</strong> {{ curMessage.sentAt | date('dd/MM/yyyy HH:mm') }}</p>
                        {% if curMessage.deliveredAt is not empty %}
                        <p><strong>Consegnato:</strong> {{ curMessage.deliveredAt | date('dd/MM/yyyy HH:mm') }}</p>
                        {% endif %}
                        {% if curMessage.openedAt is not empty %}
                        <p><strong>Aperto:</strong> {{ curMessage.openedAt | date('dd/MM/yyyy HH:mm') }}</p>
                        {% endif %}
                        {% if curMessage.errorMessage is not empty %}
                        <p class="text-red-500"><strong>Errore:</strong> {{ curMessage.errorMessage }}</p>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                {% endif %}

            </form>
        </div>
    </div>

    <!-- Sticky Footer -->
    <div class="flex-shrink-0 border-t border-gray-200 bg-white px-4 py-4 dark:border-neutral-700 dark:bg-neutral-900">
        <div class="flex justify-end gap-x-2">
            {% if curMessage is empty %}
                <!-- New Message - Show Send Button -->
                {% if user.hasPermission('MESSAGE_MANAGEMENT', 'create') %}
                <button type="submit" form="message-edit-offcanvas" class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:bg-green-700 disabled:opacity-50 disabled:pointer-events-none">
                    <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m22 2-7 20-4-9-9-4Z"/><path d="M22 2 11 13"/></svg>
                    Invia Messaggio
                </button>
                {% else %}
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Non hai i permessi per inviare messaggi
                </div>
                {% endif %}
            {% else %}
                <!-- Existing Message - Read Only -->
                <div class="text-sm text-gray-500 dark:text-neutral-400 py-2 px-3">
                    Messaggio inviato - Solo visualizzazione
                </div>
            {% endif %}
        </div>
    </div>
</div>
