{% extends "be/include/preline-base.html" %}

{% block extrahead %}

    {% set menu = 'CATALOG_COLLECTION' %}

    <title>Catalogo</title>

{% endblock %}

{% block content %}
<div class="lg:ps-65 p-2 sm:p-5 md:pt-5 space-y-5">
    <div class="max-w-6xl mx-auto">
        <!-- Tab -->
            <div class="flex flex-col lg:flex-row bg-white rounded-xl border border-gray-200 shadow-xs dark:bg-neutral-800 dark:bg-neutral-900 dark:border-neutral-700">
                <div class="shrink-0">
                    <div class="h-full lg:w-50 lg:border-e border-gray-200 dark:border-neutral-700">
                        <div class="p-4 lg:p-0">
                            <!-- Select Nav -->
                            <select id="hs-pro-vcnadpn-select" class="hidden" data-hs-select='{
                                    "placeholder": "Select option...",
                                    "toggleTag": "<button type=\"button\" aria-expanded=\"false\"><span class=\"me-2\" data-icon></span><span class=\"text-gray-800 dark:text-neutral-200 \" data-title></span></button>",
                                    "toggleClasses": "hs-select-disabled:pointer-events-none hs-select-disabled:opacity-50 relative py-2 px-2 pe-8 flex items-center text-nowrap w-full cursor-pointer bg-gray-100 text-gray-800 rounded-lg text-start text-sm dark:bg-neutral-800 dark:text-neutral-200",
                                    "wrapperClasses": "lg:hidden",
                                    "dropdownClasses": "mt-2 z-50 w-full max-h-72 p-1 space-y-0.5 bg-white border border-gray-200 rounded-lg shadow-xl overflow-hidden overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300 dark:[&::-webkit-scrollbar-track]:bg-neutral-700 dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500 dark:bg-neutral-900 dark:border-neutral-700",
                                    "optionClasses": "hs-selected:bg-gray-100 dark:hs-selected:bg-neutral-800 py-2 px-3 w-full text-sm text-gray-800 cursor-pointer hover:bg-gray-100 rounded-lg focus:outline-hidden focus:bg-gray-100 dark:bg-neutral-900 dark:hover:bg-neutral-800 dark:text-neutral-200 dark:focus:bg-neutral-800",
                                    "optionTemplate": "<div class=\"flex items-center\"><div class=\"me-3\" data-icon></div><div class=\"text-gray-800 dark:text-neutral-200 \" data-title></div></div>",
                                    "extraMarkup": "<div class=\"absolute top-1/2 end-2 -translate-y-1/2\"><svg class=\"shrink-0 size-3.5 text-gray-500 dark:text-neutral-500 \" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m7 15 5 5 5-5\"/><path d=\"m7 9 5-5 5 5\"/></svg></div>"
                                    }'>
                                <option value="#catalog-collection-all" data-hs-select-option='{
                                        "icon": "<svg class=\"shrink-0 size-4\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><rect width=\"7\" height=\"9\" x=\"3\" y=\"3\" rx=\"1\"/><rect width=\"7\" height=\"5\" x=\"14\" y=\"3\" rx=\"1\"/><rect width=\"7\" height=\"9\" x=\"14\" y=\"12\" rx=\"1\"/><rect width=\"7\" height=\"5\" x=\"3\" y=\"16\" rx=\"1\"/></svg>"
                                        }' selected>Tutti</option>
                                <option value="#catalog-collection-vehicles" data-hs-select-option='{
                                        "icon": "<svg class=\"shrink-0 size-4\" xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><circle cx=\"18.5\" cy=\"17.5\" r=\"3.5\"/><circle cx=\"5.5\" cy=\"17.5\" r=\"3.5\"/><circle cx=\"15\" cy=\"5\" r=\"1\"/><path d=\"M12 17.5V14l-3-3 4-3 2 3h2\"/></svg>"
                                        }'>Veicoli</option>
                            </select>
                            <!-- End Select Nav -->
                        </div>

                        <!-- Nav Tab -->
                        <nav id="hs-pro-vcnadpn-tabs" class="p-4 hidden lg:flex flex-col gap-1" aria-label="Tabs" role="tablist" aria-orientation="horizontal">
                            <button type="button" class="hs-tab-active:bg-gray-100 hs-tab-active:hover:bg-gray-100 hs-tab-active:focus:bg-gray-100 hs-tab-active:text-gray-800 dark:hs-tab-active:bg-neutral-700 dark:hs-tab-active:focus:bg-neutral-700 dark:hs-tab-active:hover:bg-neutral-700 dark:hs-tab-active:text-neutral-200 w-full flex items-center gap-x-2 p-2 text-start text-sm rounded-lg text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden active" id="catalog-collection-all-item" aria-selected="true" data-hs-tab="#catalog-collection-all" aria-controls="catalog-collection-all" role="tab" >
                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="7" height="9" x="3" y="3" rx="1"/><rect width="7" height="5" x="14" y="3" rx="1"/><rect width="7" height="9" x="14" y="12" rx="1"/><rect width="7" height="5" x="3" y="16" rx="1"/></svg>Tutti
                            </button>
                            <button type="button" class="hs-tab-active:bg-gray-100 hs-tab-active:hover:bg-gray-100 hs-tab-active:focus:bg-gray-100 hs-tab-active:text-gray-800 dark:hs-tab-active:bg-neutral-700 dark:hs-tab-active:focus:bg-neutral-700 dark:hs-tab-active:hover:bg-neutral-700 dark:hs-tab-active:text-neutral-200 w-full flex items-center gap-x-2 p-2 text-start text-sm rounded-lg text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:bg-gray-100 dark:text-neutral-200 dark:hover:bg-neutral-700 dark:focus:bg-neutral-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden " id="catalog-collection-vehicles-item" aria-selected="false" data-hs-tab="#catalog-collection-vehicles" aria-controls="catalog-collection-vehicles" role="tab" >
                                <svg class="shrink-0 size-3.5" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="18.5" cy="17.5" r="3.5"/><circle cx="5.5" cy="17.5" r="3.5"/><circle cx="15" cy="5" r="1"/><path d="M12 17.5V14l-3-3 4-3 2 3h2"/></svg>Veicoli
                            </button>
                        </nav>
                        <!-- End Nav Tab -->
                    </div>
                </div>

                <div class="grow">
                    <!-- Tab Content -->
                    <div id="catalog-collection-all" role="tabpanel" aria-labelledby="catalog-collection-all-item">
                        <!-- Empty State -->
                        <div class="p-5  flex flex-col justify-center items-center text-center">
                            <svg class="w-48 mx-auto mb-4" width="178" height="90" viewBox="0 0 178 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect x="27" y="50.5" width="124" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/>
                                <rect x="27" y="50.5" width="124" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-50 dark:stroke-neutral-700/10"/>
                                <rect x="34.5" y="58" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                                <rect x="66.5" y="61" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                                <rect x="66.5" y="73" width="77" height="6" rx="3" fill="currentColor" class="fill-gray-50 dark:fill-neutral-700/30"/>
                                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" fill="currentColor" class="fill-white dark:fill-neutral-800"/>
                                <rect x="19.5" y="28.5" width="139" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/30"/>
                                <rect x="27" y="36" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                                <rect x="59" y="39" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                                <rect x="59" y="51" width="92" height="6" rx="3" fill="currentColor" class="fill-gray-100 dark:fill-neutral-700/70"/>
                                <g filter="url(#filter5)">
                                    <rect x="12" y="6" width="154" height="40" rx="8" fill="currentColor" class="fill-white dark:fill-neutral-800" shape-rendering="crispEdges"/>
                                    <rect x="12.5" y="6.5" width="153" height="39" rx="7.5" stroke="currentColor" class="stroke-gray-100 dark:stroke-neutral-700/60" shape-rendering="crispEdges"/>
                                    <rect x="20" y="14" width="24" height="24" rx="4" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700 "/>
                                    <rect x="52" y="17" width="60" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/>
                                    <rect x="52" y="29" width="106" height="6" rx="3" fill="currentColor" class="fill-gray-200 dark:fill-neutral-700"/>
                                </g>
                                <defs>
                                    <filter id="filter5" x="0" y="0" width="178" height="64" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                        <feOffset dy="6"/>
                                        <feGaussianBlur stdDeviation="6"/>
                                        <feComposite in2="hardAlpha" operator="out"/>
                                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"/>
                                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1187_14810"/>
                                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1187_14810" result="shape"/>
                                    </filter>
                                </defs>
                            </svg>

                            <div class="max-w-sm mx-auto">
                                <p class="mt-2 font-medium text-gray-800 dark:text-neutral-200">
                                    Nessun prodotto disponibile
                                </p>
                                <p class="mb-5 text-sm text-gray-500 dark:text-neutral-500">
                                    Per questa categoria non sono ancora stati inseriti prodotti.
                                </p>
                            </div>

                        </div>
                        <!-- End Empty State -->
                    </div>
                    <!-- End Tab Content -->

                    <!-- Tab Content -->
                    <div id="catalog-collection-vehicles" class="hidden" role="tabpanel" aria-labelledby="catalog-collection-vehicles-item">

                        <div class="p-5 flex flex-col gap-4">

                            <!-- Card -->
                            <div class="p-4 bg-white border border-gray-200 rounded-xl shadow-2xs dark:bg-neutral-900 dark:border-neutral-700 w-full">
                                <!-- Header con titolo e prezzo -->
                                <div class="flex items-center justify-between gap-4">

                                    <!-- Titolo con icona a sinistra -->
                                    <div class="flex items-center gap-x-3">
                                        <!-- Logo -->
                                        <img class="inline-block size-15.5 rounded-full" src="{{ contextPath }}/img/brands/harley.svg" alt="Avatar">

                                            <!-- End Logo -->
                                            <div>
                                                <h3 class="font-semibold text-lg text-gray-800 dark:text-neutral-200">
                                                    Harley Davidson RC
                                                </h3>
                                                <p class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Moto
                                                </p>
                                            </div>
                                    </div>

                                    <!-- Prezzo a destra -->
                                    <div class="text-end shrink-0">
                                        <h2 class="text-2xl font-semibold text-gray-800 dark:text-neutral-200">
                                            €296
                                        </h2>
                                        <p class="text-sm text-gray-500 dark:text-neutral-500">
                                            a partire da
                                        </p>
                                    </div>

                                </div>

                                <!-- Sezione collassabile con dettagli nascosti -->
                                <div id="hs-pro-insurance-details" class="hs-collapse hidden w-full overflow-hidden transition-[height] duration-300" aria-labelledby="hs-pro-insurance-toggle">
                                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-neutral-700">
                                        <!-- Grid dettagli nascosti -->
                                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-y-4 gap-x-6">

                                            <!-- Col 1: Documenti necessari -->
                                            <div class="flex flex-col gap-y-2">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Documenti necessari:
                                                </div>
                                                <div class="flex flex-wrap justify-start items-center gap-1">
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                        Carta d'identità
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                        Libretto
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                        Passaggio di proprietà
                                                    </div>
                                                </div>
                                            </div>


                                            <!-- Col 2: Brand compatibili -->
                                            <div class="flex flex-col gap-y-2">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Brand compatibili:
                                                </div>
                                                <div class="flex flex-wrap justify-start items-center gap-1">
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <img src="{{ contextPath }}/img/brands/harley.svg" class="shrink-0 size-4" width="32" height="32">
                                                            Harley Davidson
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <img src="{{ contextPath }}/img/brands/ducati.svg" class="shrink-0 size-4" width="32" height="32">
                                                            Ducati
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <img src="{{ contextPath }}/img/brands/triumph.svg" class="shrink-0 size-4" width="32" height="32">
                                                            Triumph
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Col: Garanzie previste -->
                                            <div class="flex flex-col gap-y-2">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Garanzie previste:
                                                </div>
                                                <div class="flex flex-wrap justify-start items-center gap-1">
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"/>
                                                        </svg>
                                                        RC
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
                                                            <circle cx="9" cy="7" r="4"/>
                                                            <path d="M22 21v-2a4 4 0 0 0-3-3.87"/>
                                                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                                        </svg>
                                                        Infortuni
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z"/>
                                                        </svg>
                                                        Furto incendio
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"/>
                                                        </svg>
                                                        Tutela legale
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                                                            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
                                                        </svg>
                                                        Assistenza stradale
                                                    </div>
                                                </div>
                                            </div>

                                        </div>

                                        <!-- Descrizione completa -->
                                        <div class="mt-4">
                                            <div class="text-sm text-gray-500 dark:text-neutral-500 mb-2">
                                                Descrizione:
                                            </div>
                                            <p class="text-sm text-gray-800 dark:text-neutral-200 leading-relaxed">
                                                La polizza moto completa offre la massima protezione per il tuo veicolo. Include copertura RC obbligatoria,
                                                protezione contro furto e incendio, e copertura kasko per tutti i danni. Ideale per moto di alta gamma e
                                                per chi desidera la massima tranquillità. Preventivo veloce in soli 2 minuti con possibilità di personalizzazione
                                                delle franchigie e massimali.
                                            </p>
                                        </div>

                                        <!-- Informazioni aggiuntive -->
                                        <div class="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <div class="flex flex-col gap-y-0.5">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Durata polizza:
                                                </div>
                                                <p class="text-sm text-gray-800 dark:text-neutral-200">
                                                    12 mesi (rinnovabile)
                                                </p>
                                            </div>
                                            <div class="flex flex-col gap-y-0.5">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Tempo preventivo:
                                                </div>
                                                <p class="text-sm font-medium text-green-600 dark:text-green-400">
                                                    Solo 2 minuti
                                                </p>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                                <!-- Footer con CTA e View Details -->
                                <div class="pt-3 mt-4 border-t border-gray-200 dark:border-neutral-700">
                                    <div class="flex flex-wrap justify-between items-center gap-3">

                                        <!-- View Details Button -->
                                        <button type="button" class="hs-collapse-toggle inline-flex justify-center items-center gap-x-1 text-sm text-blue-600 decoration-2 hover:underline focus:outline-hidden focus:underline dark:text-blue-400 dark:hover:text-blue-500" id="hs-pro-insurance-toggle" aria-expanded="false" aria-controls="hs-pro-insurance-details" data-hs-collapse="#hs-pro-insurance-details">
                                            <span class="hs-collapse-open:hidden">Vedi dettagli</span>
                                            <i data-lucide="chevron-down" class="hs-collapse-open:hidden size-4"></i>
                                            <span class="hs-collapse-open:block hidden">Nascondi dettagli</span>
                                            <i data-lucide="chevron-up" class="hs-collapse-open:block hidden size-4"></i>
                                        </button>

                                        <!-- CTA Button -->
                                        <button type="button" class="py-1.5 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-2 focus:ring-blue-500">
                                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-play-icon lucide-circle-play"><path d="M9 9.003a1 1 0 0 1 1.517-.859l4.997 2.997a1 1 0 0 1 0 1.718l-4.997 2.997A1 1 0 0 1 9 14.996z"/><circle cx="12" cy="12" r="10"/></svg>
                                            Nuova quotazione
                                        </button>


                                    </div>
                                </div>

                            </div>
                            <!-- End Card -->

                            <!-- Card -->
                            <div class="p-4 bg-white border border-gray-200 rounded-xl shadow-2xs dark:bg-neutral-900 dark:border-neutral-700 w-full">
                                <!-- Header con titolo e prezzo -->
                                <div class="flex items-center justify-between gap-4">

                                    <!-- Titolo con icona a sinistra -->
                                    <div class="flex items-center gap-x-3">
                                        <!-- Logo -->
                                        <img class="inline-block size-15.5 rounded-full" src="{{ contextPath }}/img/brands/harley.svg" alt="Avatar">

                                            <!-- End Logo -->
                                            <div>
                                                <h3 class="font-semibold text-lg text-gray-800 dark:text-neutral-200">
                                                    Harley Davidson Assistenza
                                                </h3>
                                                <p class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Moto
                                                </p>
                                            </div>
                                    </div>

                                    <!-- Prezzo a destra -->
                                    <div class="text-end shrink-0">
                                        <h2 class="text-2xl font-semibold text-gray-800 dark:text-neutral-200">
                                            €86
                                        </h2>
                                        <p class="text-sm text-gray-500 dark:text-neutral-500">
                                            a partire da
                                        </p>
                                    </div>

                                </div>

                                <!-- Sezione collassabile con dettagli nascosti -->
                                <div id="hs-pro-insurance-details" class="hs-collapse hidden w-full overflow-hidden transition-[height] duration-300" aria-labelledby="hs-pro-insurance-toggle">
                                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-neutral-700">
                                        <!-- Grid dettagli nascosti -->
                                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-y-4 gap-x-6">

                                            <!-- Col 1: Documenti necessari -->
                                            <div class="flex flex-col gap-y-2">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Documenti necessari:
                                                </div>
                                                <div class="flex flex-wrap justify-start items-center gap-1">
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                        Carta d'identità
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                        Libretto
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                                            <polyline points="14,2 14,8 20,8"/>
                                                            <line x1="16" y1="13" x2="8" y2="13"/>
                                                            <line x1="16" y1="17" x2="8" y2="17"/>
                                                            <line x1="10" y1="9" x2="8" y2="9"/>
                                                        </svg>
                                                        Passaggio di proprietà
                                                    </div>
                                                </div>
                                            </div>


                                            <!-- Col 2: Brand compatibili -->
                                            <div class="flex flex-col gap-y-2">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Brand compatibili:
                                                </div>
                                                <div class="flex flex-wrap justify-start items-center gap-1">
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <img src="{{ contextPath }}/img/brands/harley.svg" class="shrink-0 size-4" width="32" height="32">
                                                            Harley Davidson
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <img src="{{ contextPath }}/img/brands/ducati.svg" class="shrink-0 size-4" width="32" height="32">
                                                            Ducati
                                                    </div>
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <img src="{{ contextPath }}/img/brands/triumph.svg" class="shrink-0 size-4" width="32" height="32">
                                                            Triumph
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Col: Garanzie previste -->
                                            <div class="flex flex-col gap-y-2">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Garanzie previste:
                                                </div>
                                                <div class="flex flex-wrap justify-start items-center gap-1">
                                                    <div class="py-1 px-2 inline-flex justify-center items-center gap-x-2 bg-white border border-gray-200 text-gray-800 text-sm font-medium cursor-pointer rounded-lg shadow-2xs align-middle dark:bg-neutral-800 dark:border-neutral-700 dark:text-neutral-300">
                                                        <svg class="shrink-0 size-4 text-blue-600 dark:text-blue-400" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                                                            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
                                                        </svg>
                                                        Assistenza stradale
                                                    </div>
                                                </div>
                                            </div>

                                        </div>

                                        <!-- Descrizione completa -->
                                        <div class="mt-4">
                                            <div class="text-sm text-gray-500 dark:text-neutral-500 mb-2">
                                                Descrizione:
                                            </div>
                                            <p class="text-sm text-gray-800 dark:text-neutral-200 leading-relaxed">
                                                La polizza moto completa offre la massima protezione per il tuo veicolo. Include copertura RC obbligatoria,
                                                protezione contro furto e incendio, e copertura kasko per tutti i danni. Ideale per moto di alta gamma e
                                                per chi desidera la massima tranquillità. Preventivo veloce in soli 2 minuti con possibilità di personalizzazione
                                                delle franchigie e massimali.
                                            </p>
                                        </div>

                                        <!-- Informazioni aggiuntive -->
                                        <div class="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4">
                                            <div class="flex flex-col gap-y-0.5">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Durata polizza:
                                                </div>
                                                <p class="text-sm text-gray-800 dark:text-neutral-200">
                                                    12 mesi (rinnovabile)
                                                </p>
                                            </div>
                                            <div class="flex flex-col gap-y-0.5">
                                                <div class="text-sm text-gray-500 dark:text-neutral-500">
                                                    Tempo preventivo:
                                                </div>
                                                <p class="text-sm font-medium text-green-600 dark:text-green-400">
                                                    Solo 2 minuti
                                                </p>
                                            </div>
                                        </div>

                                    </div>
                                </div>

                                <!-- Footer con CTA e View Details -->
                                <div class="pt-3 mt-4 border-t border-gray-200 dark:border-neutral-700">
                                    <div class="flex flex-wrap justify-between items-center gap-3">

                                        <!-- View Details Button -->
                                        <button type="button" class="hs-collapse-toggle inline-flex justify-center items-center gap-x-1 text-sm text-blue-600 decoration-2 hover:underline focus:outline-hidden focus:underline dark:text-blue-400 dark:hover:text-blue-500" id="hs-pro-insurance-toggle" aria-expanded="false" aria-controls="hs-pro-insurance-details" data-hs-collapse="#hs-pro-insurance-details">
                                            <span class="hs-collapse-open:hidden">Vedi dettagli</span>
                                            <i data-lucide="chevron-down" class="hs-collapse-open:hidden size-4"></i>
                                            <span class="hs-collapse-open:block hidden">Nascondi dettagli</span>
                                            <i data-lucide="chevron-up" class="hs-collapse-open:block hidden size-4"></i>
                                        </button>

                                        <!-- CTA Button -->
                                        <button type="button" class="py-1.5 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none focus:outline-hidden focus:ring-2 focus:ring-blue-500">
                                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-circle-play-icon lucide-circle-play"><path d="M9 9.003a1 1 0 0 1 1.517-.859l4.997 2.997a1 1 0 0 1 0 1.718l-4.997 2.997A1 1 0 0 1 9 14.996z"/><circle cx="12" cy="12" r="10"/></svg>
                                            Nuova quotazione
                                        </button>


                                    </div>
                                </div>

                            </div>
                            <!-- End Card -->

                        </div>

                    </div>
                    <!-- End Tab Content -->

                </div>
            </div>
            <!-- End Tab -->
        </div>
    </div>
{% endblock %}
{% block pagescript %}


{% endblock %}
