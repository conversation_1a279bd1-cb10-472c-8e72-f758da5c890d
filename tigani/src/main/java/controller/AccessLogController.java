package controller;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class AccessLogController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccessLogController.class.getName());

    public static TemplateViewRoute be_accesslog_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.ACCESS_LOG_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengano caricati tramite ajax
        return Core.render(Pages.BE_ACCESSLOG_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_accesslog_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.ACCESS_LOG_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("accessLogId"));
        if (oid != null) {
            AccessLog loadedAccessLog = BaseDao.getDocumentById(oid, AccessLog.class);
            attributes.put("curAccessLog", loadedAccessLog);
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_ACCESSLOG_FORM, attributes, request);
    };

    public static Route be_accesslog_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.ACCESS_LOG_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<AccessLog> loadedAccessLogs;
        List<Bson> filters = new ArrayList<>();

        // Add date range filtering if parameters are provided
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedAccessLogs = BaseDao.getDocumentsByFilters(AccessLog.class, queryOptions, loadArchived);
        } else {
            loadedAccessLogs = BaseDao.getDocumentsByFilters(AccessLog.class, queryOptions);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        if (!loadedAccessLogs.isEmpty()) {
            for (AccessLog tmpAccessLog : loadedAccessLogs) {
                List<String> row = new ArrayList<>();
                row.add(tmpAccessLog.getId().toString()); // ID for row identification

                // User ID with link
                String userIdDisplay = tmpAccessLog.getUserId() != null ? tmpAccessLog.getUserId().toString() : "N.D.";
                String userIdLink = "<a class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' accessLogId='" +
                    tmpAccessLog.getId() + "'>" + userIdDisplay + "</a>";
                row.add(userIdLink);

                row.add(StringUtils.defaultIfBlank(tmpAccessLog.getFailureReason(), "N.D."));
                row.add(DateTimeUtils.dateToString(tmpAccessLog.getCreation(), "dd/MM/YYYY HH:mm"));
                row.add("Azioni");

                dataRows.add(row);
            }
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };
}
