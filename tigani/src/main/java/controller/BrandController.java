package controller;

import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.PermissionType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

import java.util.*;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class BrandController {

    private static final Logger LOGGER = LoggerFactory.getLogger(BrandController.class.getName());

    public static TemplateViewRoute be_brand_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.BRAND_MANAGEMENT.getCode(), PermissionType.VIEW);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_BRAND_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_brand = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.BRAND_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("brandId"));
        if (oid != null) {
            Brand loadedBrand = BaseDao.getDocumentById(oid, Brand.class);
            attributes.put("curBrand", loadedBrand);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Brand loadedBrand = BaseDao.getDocumentByParentId(parentId, Brand.class);
                if (loadedBrand != null) {
                    attributes.put("curBrand", loadedBrand);
                }
            }
        }

        return Core.render(Pages.BE_BRAND, attributes, request);
    };

    public static TemplateViewRoute be_brand_form = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.BRAND_MANAGEMENT.getCode(), PermissionType.VIEW);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("brandId"));
        if (oid != null) {
            Brand loadedBrand = BaseDao.getDocumentById(oid, Brand.class);
            attributes.put("curBrand", loadedBrand);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Brand loadedBrand = BaseDao.getDocumentByParentId(parentId, Brand.class);
                if (loadedBrand != null) {
                    attributes.put("curBrand", loadedBrand);
                }
            }
        }

        // Return only the form content without the base template
        return Core.render(Pages.BE_BRAND_FORM, attributes, request);
    };

    public static Route be_brand_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.BRAND_MANAGEMENT.getCode(), PermissionType.VIEW);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Brand> loadedBrands;
        List<Bson> filters = new ArrayList<>();

        // Add date range filtering if parameters are provided
        if (params.containsKey("startDate") && StringUtils.isNotBlank(params.get("startDate"))) {
            Date startDate = DateTimeUtils.stringToDate(params.get("startDate"), "dd/MM/yyyy");
            if (startDate != null) {
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.GTE, startDate));
            }
        }

        if (params.containsKey("endDate") && StringUtils.isNotBlank(params.get("endDate"))) {
            Date endDate = DateTimeUtils.stringToDate(params.get("endDate"), "dd/MM/yyyy");
            if (endDate != null) {
                // Add one day to include the entire end date
                Date endOfDay = DateUtils.addDays(endDate, 1);
                filters.add(DaoFilters.getFilter("creation", DaoFiltersOperation.LTE, endOfDay));
            }
        }

        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);

        if (loadArchived) {
            loadedBrands = BaseDao.getDocumentsByFilters(Brand.class, queryOptions, loadArchived);
        } else {
            loadedBrands = BaseDao.getDocumentsByFilters(Brand.class, queryOptions);
        }

        // Build response using JsonObject instead of manual String concatenation
        Map<String, Object> jsonResponse = new HashMap<>();
        List<List<String>> dataRows = new ArrayList<>();

        if (!loadedBrands.isEmpty()) {
            for (Brand tmpBrand : loadedBrands) {
                List<String> row = new ArrayList<>();
                row.add(tmpBrand.getId().toString()); // ID for row identification

                // Description con link
                String descriptionLink = "<a class='text-sm font-medium text-blue-600 dark:text-neutral-200 cursor-pointer hover:underline focus:outline-hidden focus:underline' brandId='" +
                    tmpBrand.getId() + "'>" +
                    StringUtils.defaultIfBlank(tmpBrand.getDescrizione(), "N.D.") + "</a>";
                row.add(descriptionLink);

                row.add(StringUtils.defaultIfBlank(tmpBrand.getCodice(), "N.D."));
                row.add(DateTimeUtils.dateToString(tmpBrand.getCreation(), "dd/MM/YYYY"));
                row.add(DateTimeUtils.dateToString(tmpBrand.getLastUpdate(), "dd/MM/YYYY"));
                row.add("Azioni");

                dataRows.add(row);
            }
        }

        jsonResponse.put("data", dataRows);
        return Core.serializeToJson(jsonResponse);
    };

    public static Route be_brand_save = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("brandId"));

        // Determine if this is create or edit operation
        PermissionType requiredPermission = (oid != null) ? PermissionType.EDIT : PermissionType.CREATE;

        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.BRAND_MANAGEMENT.getCode(), requiredPermission);

        Brand newBrand;
        if (oid != null) {
            newBrand = BaseDao.getDocumentById(oid, Brand.class);
            RequestUtils.mergeFromParams(params, newBrand);
        } else {
            newBrand = RequestUtils.createFromParams(params, Brand.class);
        }

        if (newBrand != null) {
            if (oid == null) {
                oid = BaseDao.insertDocument(newBrand);
                newBrand.setId(oid);

                BaseDao.insertLog(user, newBrand, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newBrand, user);
                BaseDao.insertLog(user, newBrand, LogType.UPDATE);
            }
        }

        // se errore ritorno Spark.halt()
        return oid;
    };

    public static Route be_brand_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String operation = params.get("operation");
        PermissionType requiredPermission = PermissionType.EDIT;
        if (StringUtils.equalsIgnoreCase(operation, "delete")) {
            requiredPermission = PermissionType.DELETE;
        }

        User user = Core.initializeRoute(request, response, attributes,
            RoutesPermission.BRAND_MANAGEMENT.getCode(), requiredPermission);

        String brandIds = params.get("brandIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(brandIds)) {
            String[] ids = brandIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Brand tmpBrand = BaseDao.getDocumentById(oid, Brand.class);
                    if (tmpBrand != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(tmpBrand, user);
                                BaseDao.insertLog(user, tmpBrand, LogType.DELETE);
                                break;
                            case "archive":
                                tmpBrand.setArchived(true);
                                BaseDao.updateDocument(tmpBrand, user);
                                BaseDao.insertLog(user, tmpBrand, LogType.UPDATE);
                                break;
                            case "unarchive":
                                tmpBrand.setArchived(false);
                                BaseDao.updateDocument(tmpBrand, user);
                                BaseDao.insertLog(user, tmpBrand, LogType.UPDATE);
                                break;
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
