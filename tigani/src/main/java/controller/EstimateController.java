package controller;

import core.Core;
import core.Pages;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class EstimateController {

    private static final Logger LOGGER = LoggerFactory.getLogger(EstimateController.class.getName());

    public static TemplateViewRoute be_estimate_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes);
        if (user == null) {
            return Core.render(Pages.BE_LOGIN, attributes, request);
        }

        return Core.render(Pages.BE_ESTIMATE_COLLECTION, attributes, request);
    };
    
    public static TemplateViewRoute be_contract_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes);
        if (user == null) {
            return Core.render(Pages.BE_LOGIN, attributes, request);
        }

        return Core.render(Pages.BE_CONTRACT_COLLECTION, attributes, request);
    };

}
